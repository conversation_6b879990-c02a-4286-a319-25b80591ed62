* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: #f2f2f7;
    line-height: 1.47059;
    color: #1c1c1e;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    user-select: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    min-height: 100vh;
    background-color: #f2f2f7;
}

.header {
    background: rgba(255, 255, 255, 0.72);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: #1c1c1e;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid rgba(60, 60, 67, 0.12);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 0 20px 0;
    min-height: auto;
}

.header h1 {
    font-size: 17px;
    font-weight: 600;
    letter-spacing: -0.41px;
    margin-bottom: 10px;
    text-align: center;
    padding: 0 16px;
}

.user-actions {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
    align-items: flex-start;
    padding-left: 8px;
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.user-actions::-webkit-scrollbar {
    display: none;
}

.cart-section {
    display: flex;
    align-items: center;
}


.admin-btn {
    font-size: 12px;
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    background: transparent;
    transition: all 0.2s ease;
    color: #8e44ad;
    border: 1px solid #8e44ad;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
}

.admin-btn:hover {
    background: rgba(142, 68, 173, 0.1);
}

.admin-btn:active {
    background: rgba(142, 68, 173, 0.2);
    transform: scale(0.95);
}

.login-btn {
    font-size: 12px;
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    background: transparent;
    transition: all 0.2s ease;
    color: #007aff;
    border: 1px solid #007aff;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
}

.login-btn:hover {
    background: rgba(0, 122, 255, 0.1);
}

.login-btn:active {
    background: rgba(0, 122, 255, 0.2);
    transform: scale(0.95);
}

.review-btn {
    font-size: 12px;
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    background: transparent;
    transition: all 0.2s ease;
    color: #ff9500;
    border: 1px solid #ff9500;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
}

.review-btn:hover {
    background: rgba(255, 149, 0, 0.1);
}

.review-btn:active {
    background: rgba(255, 149, 0, 0.2);
    transform: scale(0.95);
}

.request-btn {
    font-size: 12px;
    cursor: pointer;
    padding: 6px 10px;
    border-radius: 6px;
    background: transparent;
    transition: all 0.2s ease;
    color: #34c759;
    border: 1px solid #34c759;
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0;
}

.request-btn:hover {
    background: rgba(52, 199, 89, 0.1);
}

.request-btn:active {
    background: rgba(52, 199, 89, 0.2);
    transform: scale(0.95);
}

.cart-icon {
    position: relative;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    background: transparent;
    transition: background 0.2s ease;
    color: #007aff;
    margin-right: 4px;
    flex-shrink: 0;
}

.cart-icon:hover {
    background: rgba(0, 122, 255, 0.1);
}

.cart-icon:active {
    background: rgba(0, 122, 255, 0.2);
    transform: scale(0.95);
}

.cart-icon span {
    position: absolute;
    top: 6px;
    right: 6px;
    background: #ff3b30;
    color: white;
    border-radius: 10px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.72);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.categories {
    background: rgba(255, 255, 255, 0.72);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 12px;
    margin: 10px 0 0 0;
    border-bottom: 1px solid rgba(60, 60, 67, 0.12);
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 120px;
    min-height: 100vh;
    position: fixed;
    left: 0;
    top: 50000px;
    z-index: 50;
}

.categories::-webkit-scrollbar {
    display: none;
}

.category-btn {
    background: transparent;
    border: none;
    padding: 12px 16px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    touch-action: manipulation;
    color: #8e8e93;
    white-space: nowrap;
    letter-spacing: -0.24px;
    text-align: center;
    width: 100%;
}

.category-btn:hover {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.category-btn.active {
    background: #007aff;
    color: white;
    font-weight: 600;
}

.menu {
    margin: 0;
    padding: 0 16px 0 136px;
}

.menu-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0;
    padding: 0;
}

.menu-item {
    background: rgba(255, 255, 255, 0.72);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 0;
    overflow: hidden;
    margin: 0;
    border-bottom: 1px solid rgba(60, 60, 67, 0.12);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 12px;
}

.menu-item.visible {
    display: flex;
}

.menu-item.hidden {
    display: none;
}

.menu-item:active {
    background: rgba(0, 122, 255, 0.1);
    transform: scale(0.98);
}

.menu-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 12px;
    flex-shrink: 0;
}

.menu-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
}

.menu-item h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
    margin: 0;
    letter-spacing: -0.32px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.menu-item p {
    color: #8e8e93;
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
    letter-spacing: -0.08px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

.price {
    font-size: 18px;
    font-weight: 600;
    color: #ff3b30;
    margin: 0;
    letter-spacing: -0.41px;
}

.add-btn {
    background: #007aff;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 20px;
    transition: all 0.2s ease;
    touch-action: manipulation;
    letter-spacing: -0.24px;
    white-space: nowrap;
    flex-shrink: 0;
    align-self: center;
}

.add-btn:hover {
    background: #0051d5;
}

.add-btn:active {
    background: #003d99;
    transform: scale(0.95);
}

.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: none;
    align-items: flex-end;
    z-index: 1000;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.cart-overlay.active {
    display: flex;
}

.cart {
    background: rgba(255, 255, 255, 0.72);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    width: 100%;
    max-height: 75vh;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
    border-top: 1px solid rgba(60, 60, 67, 0.12);
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.cart-header {
    background: transparent;
    color: #1c1c1e;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(60, 60, 67, 0.12);
}

.cart-header h2 {
    font-size: 17px;
    font-weight: 600;
    letter-spacing: -0.41px;
}

.close-btn {
    background: transparent;
    border: none;
    color: #1c1c1e;
    font-size: 22px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
    font-weight: 300;
}

.close-btn:hover {
    background: rgba(0, 122, 255, 0.1);
}

.close-btn:active {
    background: rgba(0, 122, 255, 0.2);
    transform: scale(0.95);
}

.cart-items {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid rgba(60, 60, 67, 0.12);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
    letter-spacing: -0.32px;
}

.cart-item-price {
    color: #8e8e93;
    font-size: 14px;
    letter-spacing: -0.08px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.quantity-btn {
    background: transparent;
    border: 1px solid rgba(60, 60, 67, 0.3);
    width: 32px;
    height: 32px;
    border-radius: 16px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #007aff;
}

.quantity-btn:hover {
    background: rgba(0, 122, 255, 0.1);
}

.quantity-btn:active {
    background: rgba(0, 122, 255, 0.2);
    transform: scale(0.95);
}

.quantity {
    font-weight: 600;
    min-width: 24px;
    text-align: center;
    font-size: 16px;
    letter-spacing: -0.32px;
}

.remove-btn {
    background: transparent;
    color: #ff3b30;
    border: 1px solid #ff3b30;
    padding: 6px 12px;
    border-radius: 16px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    letter-spacing: -0.08px;
}

.remove-btn:hover {
    background: rgba(255, 59, 48, 0.1);
}

.remove-btn:active {
    background: rgba(255, 59, 48, 0.2);
    transform: scale(0.95);
}

.empty-cart {
    text-align: center;
    color: #8e8e93;
    padding: 60px 20px;
    font-size: 16px;
    letter-spacing: -0.32px;
}

.cart-footer {
    background: transparent;
    padding: 16px;
    border-top: 1px solid rgba(60, 60, 67, 0.12);
}

.total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: -0.32px;
}

.total span:last-child {
    color: #ff3b30;
}

.checkout-btn {
    width: 100%;
    padding: 14px;
    background: #007aff;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    letter-spacing: -0.32px;
}

.checkout-btn:hover {
    background: #0051d5;
}

.checkout-btn:active {
    background: #003d99;
    transform: scale(0.98);
}

.admin-view-btn {
    width: 100%;
    padding: 14px;
    background: #8e44ad;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    letter-spacing: -0.32px;
    margin-top: 10px;
}

.admin-view-btn:hover {
    background: #7d3c98;
}

.admin-view-btn:active {
    background: #6c3483;
    transform: scale(0.98);
}

.success-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.72);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: #1c1c1e;
    padding: 20px 40px;
    border-radius: 20px;
    font-size: 17px;
    font-weight: 600;
    display: none;
    z-index: 2000;
    animation: fadeInOut 2s ease-in-out;
    border: 1px solid rgba(60, 60, 67, 0.12);
    letter-spacing: -0.41px;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    10%, 90% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.menu-item:active {
    animation: pulse 0.3s ease-in-out;
}

.cart-icon:active {
    animation: bounce 0.4s ease-in-out;
}

@media (max-width: 768px) {
    .container {
        padding: 0;
    }
    
    .header {
        padding: 8px 0 8px 86px;
    }
    
    .review-btn, .request-btn, .admin-btn, .login-btn {
        font-size: 11px;
        padding: 5px 8px;
    }
    
    .header h1 {
        font-size: 17px;
    }
    
    .menu-grid {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .menu {
        padding: 0 16px 0 96px;
    }
    
    .categories {
        padding: 12px;
        margin: 0;
        width: 80px;
        min-height: 100vh;
        position: fixed;
        left: 0;
        top: 100px;
        z-index: 50;
        flex-direction: column;
    }
    
    .category-btn {
        padding: 8px 4px;
        font-size: 12px;
        width: 100%;
        text-align: center;
        border-radius: 8px;
    }
    
    .cart {
        max-height: 70vh;
    }
}

/* 中等屏幕设备优化 */
@media (max-width: 1024px) {
    .categories {
        width: 100px;
        top: 100px;
    }
    
    .header {
        padding: 8px 0 8px 106px;
    }
    
    .menu {
        padding: 0 16px 0 116px;
    }
    
    .category-btn {
        font-size: 13px;
        padding: 10px 8px;
    }
    
    .menu-item h3 {
        max-width: 100px;
    }
    
    .menu-item p {
        max-width: 100px;
    }
}

/* 超小屏幕设备优化 */
@media (max-width: 480px) {
    .categories {
        width: 70px;
        padding: 8px 6px;
        top: 95px;
    }
    
    .header {
        padding: 8px 0 8px 76px;
    }
    
    .header h1 {
        font-size: 14px;
    }
    
    .header-actions {
        gap: 3px;
    }
    
    .review-btn, .request-btn, .admin-btn, .login-btn {
        padding: 4px 6px;
        font-size: 10px;
    }
    
    .cart-icon {
        font-size: 18px;
        padding: 8px;
    }
    
    .cart-icon span {
        width: 16px;
        height: 16px;
        font-size: 10px;
        top: 4px;
        right: 4px;
    }
    
    .cart-icon {
        font-size: 16px;
        padding: 6px;
    }
    
    .menu {
        padding: 0 8px 0 86px;
    }
    
    .category-btn {
        padding: 8px 4px;
        font-size: 11px;
        border-radius: 6px;
        line-height: 1.3;
        text-align: center;
        width: calc(100% - 8px);
        margin: 2px;
    }
    
    .menu-item {
        padding: 8px;
        gap: 8px;
    }
    
    .menu-item img {
        width: 60px;
        height: 60px;
        border-radius: 8px;
    }
    
    .menu-item h3 {
        font-size: 14px;
        max-width: 80px;
    }
    
    .menu-item p {
        font-size: 11px;
        max-width: 80px;
    }
    
    .price {
        font-size: 14px;
    }
    
    .add-btn {
        padding: 6px 10px;
        font-size: 12px;
        border-radius: 15px;
    }
    
    .admin-view-btn {
        padding: 10px;
        font-size: 14px;
    }
    
    .cart {
        max-height: 65vh;
        border-radius: 15px 15px 0 0;
    }
    
    .cart-header {
        padding: 15px;
    }
    
    .cart-header h2 {
        font-size: 18px;
    }
    
    .cart-items {
        padding: 0 15px;
    }
    
    .cart-footer {
        padding: 15px;
    }
    
    .cart-item {
        padding: 10px 0;
    }
    
    .cart-item-name {
        font-size: 14px;
    }
    
    .cart-item-price {
        font-size: 12px;
    }
    
    .quantity-btn {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }
    
    .quantity {
        font-size: 12px;
        min-width: 20px;
    }
    
    .remove-btn {
        font-size: 10px;
        padding: 4px 8px;
    }
    
    .total {
        font-size: 16px;
    }
    
    .checkout-btn, .admin-view-btn {
        padding: 8px;
        font-size: 12px;
    }
}