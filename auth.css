/* 认证页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.auth-container {
    width: 100%;
    max-width: 450px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
    animation: containerGlow 4s ease-in-out infinite alternate;
}

@keyframes containerGlow {
    0% {
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    }
    100% {
        box-shadow: 0 20px 60px rgba(240, 147, 251, 0.3);
    }
}

.auth-card {
    padding: 40px 30px;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    font-size: 28px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.auth-header p {
    color: #666;
    font-size: 16px;
}

.auth-tabs {
    display: flex;
    background: #f5f5f5;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 30px;
}

.auth-tab {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.auth-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 200% 200%;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    animation: tabGradient 3s ease infinite;
}

@keyframes tabGradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.auth-forms {
    position: relative;
    min-height: 520px;
}

.auth-form {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.auth-form.active {
    opacity: 1;
    transform: translateX(0);
    pointer-events: all;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #764ba2;
}

.auth-btn {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 200% 200%;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    animation: buttonGradient 4s ease infinite;
}

@keyframes buttonGradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    animation-duration: 2s;
}

.auth-btn:active {
    transform: translateY(0);
}

.form-agreement {
    margin-bottom: 25px;
}

.agree-terms {
    display: flex;
    align-items: flex-start;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.agree-terms input {
    margin-right: 8px;
    margin-top: 2px;
}

.terms-link, .privacy-link {
    color: #667eea;
    text-decoration: none;
}

.terms-link:hover, .privacy-link:hover {
    color: #764ba2;
}

.auth-divider {
    text-align: center;
    margin: 30px 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.auth-divider span {
    background: white;
    padding: 0 15px;
    color: #666;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.social-login {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: 2px solid #e1e5e9;
    background: linear-gradient(45deg, #ffffff 0%, #f8f9ff 100%);
    background-size: 200% 200%;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: socialBtnGradient 6s ease infinite;
}

@keyframes socialBtnGradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.social-btn:hover {
    border-color: #667eea;
    background: linear-gradient(45deg, #f8f9ff 0%, #e8f0ff 100%);
    animation-duration: 2s;
}

.social-icon {
    margin-right: 8px;
    font-size: 18px;
}

.wechat-btn {
    color: #07c160;
}

.phone-btn {
    color: #667eea;
}

.auth-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.auth-footer p {
    color: #666;
    font-size: 14px;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    color: #764ba2;
}

.success-message, .error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.success-message {
    background: #28a745;
}

.error-message {
    background: #dc3545;
}

.success-message.show, .error-message.show {
    transform: translateX(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
    .auth-container {
        margin: 10px;
        border-radius: 15px;
    }
    
    .auth-card {
        padding: 30px 20px;
    }
    
    .auth-header h1 {
        font-size: 24px;
    }
    
    .auth-tabs {
        padding: 3px;
    }
    
    .auth-tab {
        padding: 10px 16px;
        font-size: 14px;
    }
}