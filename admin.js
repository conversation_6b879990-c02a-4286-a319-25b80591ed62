// 管理员页面JavaScript功能
let orders = JSON.parse(localStorage.getItem('restaurantOrders')) || [];

document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

function initializeAdmin() {
    updateStats();
    displayOrders();
}

function updateStats() {
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    const totalItems = orders.reduce((sum, order) => sum + order.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0);

    document.getElementById('total-orders').textContent = totalOrders;
    document.getElementById('total-revenue').textContent = `¥${totalRevenue}`;
    document.getElementById('total-items').textContent = totalItems;
}

function displayOrders() {
    const container = document.getElementById('orders-container');
    
    if (orders.length === 0) {
        container.innerHTML = '<div class="no-orders">暂无订单</div>';
        return;
    }

    const ordersHTML = orders.map((order, index) => `
        <div class="order-card">
            <div class="order-header">
                <div class="order-id">订单 #${String(index + 1).padStart(4, '0')}</div>
                <div class="order-time">${order.time}</div>
            </div>
            <div class="order-items">
                ${order.items.map(item => `
                    <div class="order-item">
                        <div class="item-info">
                            <span class="item-name">${item.name}</span>
                            <span class="item-quantity">×${item.quantity}</span>
                        </div>
                        <div class="item-price">¥${item.price * item.quantity}</div>
                    </div>
                `).join('')}
            </div>
            <div class="order-footer">
                <div class="order-total">总计：¥${order.total}</div>
                <div class="order-status status-completed">已完成</div>
            </div>
        </div>
    `).join('');

    container.innerHTML = ordersHTML;
}

function goBack() {
    window.location.href = 'index.html';
}

function clearAllOrders() {
    if (orders.length === 0) {
        showMessage('暂无订单需要清空', 'error');
        return;
    }

    if (confirm('确定要清空所有订单吗？此操作不可恢复。')) {
        orders = [];
        localStorage.removeItem('restaurantOrders');
        updateStats();
        displayOrders();
        showMessage('所有订单已清空', 'success');
    }
}

function showMessage(message, type = 'success') {
    const messageElement = document.getElementById(type + '-message');
    messageElement.textContent = message;
    messageElement.classList.add('show');
    
    setTimeout(() => {
        messageElement.classList.remove('show');
    }, 3000);
}

// 定期刷新订单显示（如果多个管理员页面同时打开）
setInterval(() => {
    const latestOrders = JSON.parse(localStorage.getItem('restaurantOrders')) || [];
    if (JSON.stringify(latestOrders) !== JSON.stringify(orders)) {
        orders = latestOrders;
        updateStats();
        displayOrders();
    }
}, 5000);