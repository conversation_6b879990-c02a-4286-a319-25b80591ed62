/* 管理员页面样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.admin-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-header h1 {
    font-size: 24px;
    font-weight: 600;
}

.admin-actions {
    display: flex;
    gap: 10px;
}

.back-btn, .clear-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn {
    background: #3498db;
    color: white;
}

.back-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.clear-btn {
    background: #e74c3c;
    color: white;
}

.clear-btn:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 30px;
    background: #f8f9fa;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.admin-main {
    padding: 30px;
}

.orders-section h2 {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.orders-container {
    display: grid;
    gap: 20px;
}

.order-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3498db;
    transition: transform 0.3s ease;
}

.order-card:hover {
    transform: translateY(-2px);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.order-id {
    font-weight: 600;
    color: #2c3e50;
}

.order-time {
    font-size: 12px;
    color: #666;
}

.order-items {
    margin-bottom: 15px;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.order-item:last-child {
    border-bottom: none;
}

.item-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-name {
    font-weight: 500;
    color: #2c3e50;
}

.item-quantity {
    background: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.item-price {
    font-weight: 600;
    color: #27ae60;
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    border-top: 2px solid #f5f5f5;
}

.order-total {
    font-size: 18px;
    font-weight: 700;
    color: #e74c3c;
}

.order-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.no-orders {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    font-size: 16px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px dashed #ddd;
}

.success-message, .error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.success-message {
    background: #28a745;
}

.error-message {
    background: #dc3545;
}

.success-message.show, .error-message.show {
    transform: translateX(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .admin-stats {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .admin-main {
        padding: 20px;
    }
    
    .order-header {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .order-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
}