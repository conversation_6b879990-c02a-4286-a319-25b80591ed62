let cart = [];
let currentCategory = 'all';

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    setupCategoryFilters();
    setupCartOverlay();
    updateCartDisplay();
    initializeMenuItems();
}

function initializeMenuItems() {
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.classList.add('visible');
    });
}

function setupCategoryFilters() {
    const categoryBtns = document.querySelectorAll('.category-btn');
    
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            categoryBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            currentCategory = this.dataset.category;
            filterMenuItems();
        });
    });
}

function filterMenuItems() {
    const menuItems = document.querySelectorAll('.menu-item');
    
    menuItems.forEach(item => {
        if (currentCategory === 'all' || item.dataset.category === currentCategory) {
            item.classList.remove('hidden');
            item.classList.add('visible');
        } else {
            item.classList.remove('visible');
            item.classList.add('hidden');
        }
    });
}

function setupCartOverlay() {
    const cartOverlay = document.getElementById('cart-overlay');
    
    cartOverlay.addEventListener('click', function(e) {
        if (e.target === cartOverlay) {
            toggleCart();
        }
    });
}

function addToCart(name, price) {
    const existingItem = cart.find(item => item.name === name);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            name: name,
            price: price,
            quantity: 1
        });
    }
    
    updateCartDisplay();
    showAddToCartFeedback();
}

function showAddToCartFeedback() {
    const cartIcon = document.querySelector('.cart-icon');
    cartIcon.style.transform = 'scale(1.1)';
    cartIcon.style.background = 'rgba(0, 122, 255, 0.2)';
    
    setTimeout(() => {
        cartIcon.style.transform = 'scale(1)';
        cartIcon.style.background = 'transparent';
    }, 150);
}

function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
}

function updateQuantity(index, change) {
    const item = cart[index];
    item.quantity += change;
    
    if (item.quantity <= 0) {
        removeFromCart(index);
    } else {
        updateCartDisplay();
    }
}

function updateCartDisplay() {
    const cartCount = document.getElementById('cart-count');
    const cartItems = document.getElementById('cart-items');
    const totalPrice = document.getElementById('total-price');
    
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    cartCount.textContent = totalItems;
    
    if (cart.length === 0) {
        cartItems.innerHTML = '<p class="empty-cart">购物车为空</p>';
        totalPrice.textContent = '¥0';
        return;
    }
    
    let cartHTML = '';
    let total = 0;
    
    cart.forEach((item, index) => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        cartHTML += `
            <div class="cart-item">
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">¥${item.price} × ${item.quantity}</div>
                </div>
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity(${index}, -1)">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity(${index}, 1)">+</button>
                    <button class="remove-btn" onclick="removeFromCart(${index})">删除</button>
                </div>
            </div>
        `;
    });
    
    cartItems.innerHTML = cartHTML;
    totalPrice.textContent = `¥${total}`;
}

function toggleCart() {
    const cartOverlay = document.getElementById('cart-overlay');
    cartOverlay.classList.toggle('active');
    
    if (cartOverlay.classList.contains('active')) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = 'auto';
    }
}

function checkout() {
    if (cart.length === 0) {
        return;
    }
    
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const orderDetails = cart.map(item => `${item.name} × ${item.quantity}`).join(', ');
    
    console.log('订单详情:', orderDetails);
    console.log('总金额:', total);
    
    // 保存订单到本地存储
    saveOrder(cart, total);
    
    cart = [];
    updateCartDisplay();
    toggleCart();
    
    showSuccessMessage();
    
    // 延迟跳转到管理员页面
    setTimeout(() => {
        window.location.href = 'admin.html';
    }, 1500);
}

function saveOrder(items, total) {
    const orders = JSON.parse(localStorage.getItem('restaurantOrders')) || [];
    
    const newOrder = {
        id: Date.now(),
        items: items.map(item => ({
            name: item.name,
            price: item.price,
            quantity: item.quantity
        })),
        total: total,
        time: new Date().toLocaleString('zh-CN')
    };
    
    orders.push(newOrder);
    localStorage.setItem('restaurantOrders', JSON.stringify(orders));
}

function showSuccessMessage() {
    const successMessage = document.getElementById('success-message');
    successMessage.style.display = 'block';
    
    setTimeout(() => {
        successMessage.style.display = 'none';
    }, 2000);
}

document.addEventListener('touchstart', function() {
}, { passive: true });

document.addEventListener('touchmove', function(e) {
    const cartOverlay = document.getElementById('cart-overlay');
    if (cartOverlay.classList.contains('active')) {
        e.preventDefault();
    }
}, { passive: false });

window.addEventListener('resize', function() {
    if (window.innerWidth > 768) {
        const cartOverlay = document.getElementById('cart-overlay');
        if (cartOverlay.classList.contains('active')) {
            document.body.style.overflow = 'hidden';
        }
    }
});

document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const cartOverlay = document.getElementById('cart-overlay');
        if (cartOverlay.classList.contains('active')) {
            toggleCart();
        }
    }
});

function goToAuth() {
    window.location.href = 'auth.html';
}

function goToAdmin() {
    window.location.href = 'admin.html';
}

function setupSwipeGestures() {
    let touchStartX = 0;
    let touchStartY = 0;
    let touchEndX = 0;
    let touchEndY = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
        touchStartY = e.changedTouches[0].screenY;
    });
    
    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        touchEndY = e.changedTouches[0].screenY;
        handleSwipe();
    });
    
    function handleSwipe() {
        const cartOverlay = document.getElementById('cart-overlay');
        const swipeThreshold = 50;
        const verticalThreshold = 100;
        
        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;
        
        if (cartOverlay.classList.contains('active')) {
            if (deltaX > swipeThreshold && Math.abs(deltaY) < verticalThreshold) {
                toggleCart();
            }
        }
    }
}

setupSwipeGestures();

// 评价功能
function openReviews() {
    showNotification('评价功能开发中...', 'info');
}

// 要求功能
function openRequests() {
    showNotification('要求功能开发中...', 'info');
}

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        z-index: 1001;
        transform: translateX(400px);
        transition: transform 0.3s ease;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;
    
    // 根据类型设置背景色
    const colors = {
        'info': '#007aff',
        'success': '#34c759',
        'warning': '#ff9500',
        'error': '#ff3b30'
    };
    notification.style.backgroundColor = colors[type] || colors['info'];
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动消失
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}